﻿=== Fancy Elementor Flipbox ===
Contributors: hosseinhashemi
Tags: elementor, hover-effects, flip-box, elementor addon, elementor widget
Description: create flip box and 6 more effects with front and back side
Short Description: create flip box and 6 more effects with front and back side
Donate link: https://themeprix.com/donate
Requires at least: 4.0
Tested up to: 6.8
Requires PHP: 7.4
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html
Plugin URI: https://themeprix.com/fancy-elementor-flipbox/
Author URI: https://themeprix.com/
Stable tag: 2.6.1

Create flip box and 6 more effects with front and back side options


== Description ==
Fancy Elementor Flipbox for Elementor WordPress Page Builder. A fully free and endless customization Flipbox, Rotate Style, Zoom In Style, Right-Side, Left-side, To Top, To Bottom and Zoom Out Style. You could use this plugin to show Services, Features, Categories, team members, contact info boxes, portfolio and image galleries.

== Short Description ==
Fancy Elementor Flipbox for Elementor WordPress Page Builder.

= Check out Demos =
* [Check demos](https://themeprix.com/fancy-elementor-flipbox/) — This plugin has many features that you could use it for many purposes.


== Installation ==
Note : This plugin works with Elementor. Make sure you have [Elementor](https://wordpress.org/plugins/elementor/) installed.


1. Upload the plugin folder to the `/wp-content/plugins/` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the \'Plugins\' screen in WordPress
3. You can type \"Fancy Elementor Flipbox\" on your element tabs within Elementor editor and all the flipbox element will appear.
4. Done! Enjoy :)


== Screenshots ==
1. Use flip-box as photo gallery.
2. Options and features.
3. Services demo.
4. Creative Branding demo.
5. Use flip-box elements as team members items.


== Frequently Asked Questions ==
= How many styles does it have? =
8 Styles. Flipbox, Rotate Style, Zoom In Style, Right Side, Left Side and Zoom Out Style.

= Could I use this plugin without Elementor Page Builder? =
No. You could not use without Elementor since it's a widget for Elementor.

= Does it work with any theme? =
Sure! It will work with any theme where Elementor works.

= Does it support Elementor Pro? =
Yes, you could use the dynamic fields if you install the Elementor Pro.

= Is it a free plugin? =
Yes, it's a free plugin. We don't have any premium version and we don't show you any go to pro box on your dashboard.

= Is it possible to request new features? =
Yes, It's possible. Please request your needs on this page: (https://themeprix.com/order/)

== Changelog ==
= 2.5.0 =
* Update codes for Elementor and a new fade style added as update 2024.

= 2.4.2 =
* Update codes for Elementor update 2022.

= 2.4.1 =
* Add two new styles.
* Update codes for new Elementor update.

= 2.3.2 =
* Add two new styles.
* Make it SEO-Freindly for images.
* Bug fix for flipbox height.

= 1.0.3 =
* Add dynamic field for Elementor Pro

= 1.0.0 =
* Initial Launch

= 1.0.6 =
* Fix a minor error reported by users.
